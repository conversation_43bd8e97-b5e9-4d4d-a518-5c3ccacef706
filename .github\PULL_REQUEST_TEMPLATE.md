## ❤ Thank you for contributing to GitLens! ❤

### 🚨 IMPORTANT 🚨

- Please create an issue _before_ creating a Pull Request
- Please use the following Git commit message style
  - Use future tense ("Adds feature" not "Added feature")
  - Use a "Fixes #xxx -" or "Closes #xxx -" prefix to auto-close the issue that your PR addresses
  - Limit the first line to 72 characters or less
  - Reference issues and pull requests liberally after the first line

## ↑👆 DELETE above _before_ submitting 👆↑

---

# Description

<!--
Please include a summary of the changes and which issue will be addressed. Please also include relevant motivation and context.
-->

# Checklist

<!-- Please check off the following -->

- [ ] I have followed the guidelines in the Contributing document
- [ ] My changes follow the coding style of this project
- [ ] My changes build without any errors or warnings
- [ ] My changes have been formatted and linted
- [ ] My changes include any required corresponding changes to the documentation (including CHANGELOG.md and README.md)
- [ ] My changes have been rebased and squashed to the minimal number (typically 1) of relevant commits
- [ ] My changes have a descriptive commit message with a short title, including a `Fixes $XXX -` or `Closes #XXX -` prefix to auto-close the issue that your PR addresses
