[{"kind": 2, "language": "github-issues", "value": "$current=milestone:\"13.1\"\n$patch=milestone:\"13.0-patch\"\n$issues=repo:gitkraken/vscode-gitlens is:issue\n$prs=repo:gitkraken/vscode-gitlens is:pr"}, {"kind": 1, "language": "markdown", "value": "## Triage"}, {"kind": 1, "language": "markdown", "value": "### Inbox"}, {"kind": 2, "language": "github-issues", "value": "$issues is:open label:\"triage\" sort:updated-desc"}, {"kind": 1, "language": "markdown", "value": "### Needs More Information"}, {"kind": 2, "language": "github-issues", "value": "$issues label:\"needs-more-info\" sort:updated-desc"}, {"kind": 1, "language": "markdown", "value": "## Patch"}, {"kind": 2, "language": "github-issues", "value": "$issues $patch"}, {"kind": 1, "language": "markdown", "value": "## Current Iteration"}, {"kind": 1, "language": "markdown", "value": "### TODO"}, {"kind": 2, "language": "github-issues", "value": "$issues $current is:open"}, {"kind": 1, "language": "markdown", "value": "### Verification Needed"}, {"kind": 2, "language": "github-issues", "value": "$issues $current is:closed label:\"needs-verification\" -label:\"verified ✔\""}, {"kind": 1, "language": "markdown", "value": "### Done"}, {"kind": 2, "language": "github-issues", "value": "$issues $current is:closed -label:\"pending-release\""}, {"kind": 1, "language": "markdown", "value": "## Milestones"}, {"kind": 1, "language": "markdown", "value": "### Soon™"}, {"kind": 2, "language": "github-issues", "value": "$issues milestone:Soon™"}, {"kind": 1, "language": "markdown", "value": "## Pull Requests"}, {"kind": 2, "language": "github-issues", "value": "$prs"}]