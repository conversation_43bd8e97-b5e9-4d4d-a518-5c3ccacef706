# Webview IPC, Embedding, and Shared State Refactor Spec

Status: Draft (v0.1)
Owner: (TBD)
Reviewers: <PERSON>raph, Commit Composer, Home, AI, Webviews, Core Platform teams
Target Phases: Q4 2025 – Q1 2026

## 1. Overview

GitLens currently operates multiple independent VS Code webviews (Graph, Home, Commit Composer, etc.). Each webview duplicates IPC plumbing, local state management, and serialization logic. This creates maintenance overhead, inconsistent UX, and impedes scenarios like embedding the Commit Composer or Home experience inside the Commit Graph without opening a separate panel.

This spec defines a unified, strongly-typed, extensible IPC and state model plus an “embedded micro‑app” architecture enabling multiple logical webview applications to coexist (side-by-side or nested) within a single VS Code webview surface (the *Host Shell*). It also supports traditional standalone webviews for backward compatibility during phased migration.

## 2. Goals

1. Enable seamless embedding of logical webview apps ("micro-apps") inside another (e.g., Commit Composer + Home inside Graph) without spawning additional VS Code webview panels.
2. Provide a shared, selectively synchronized state layer across all micro-apps and host, reducing duplication of repo/session/user/subscription/cache data.
3. Introduce a type-safe, versioned IPC protocol (request/response, events, streaming) with code generation and runtime validation—compatible with VS Code `postMessage` JSON constraints.
4. Support lazy loading, isolation, and lifecycle control of embedded micro-apps (mount/unmount, focus, disposal) with minimal performance overhead.
5. Improve serialization (Dates, enums, discriminated unions) while guaranteeing deterministic, secure transport (no arbitrary prototype pollution, no functions).
6. Provide a low-risk migration path; existing webviews continue to work during incremental adoption.
7. Improve debuggability (structured logs, tracing IDs, message inspection, performance timing).
8. Support partial state hydration + diff/patch updates to reduce message volume.
9. Transparent embedding: any micro-app expressed as a custom element can be instantiated inside any host webview without additional bootstrapping code.

## 3. Non-Goals

* Not replacing VS Code's core webview security model (content security policy, sandbox).
* Not introducing multi-process isolation or iframes (unless later required for third-party plugin sandboxing).
* Not rewriting existing UI frameworks—Lit remains the base.
* Not introducing persistent offline storage beyond existing mechanisms.
* Not providing real-time cross-machine collaboration (future potential, but out of scope now).

## 4. Current Pain Points

| Area | Issue |
|------|-------|
| IPC | Ad-hoc message shapes, weak typing, duplicated enums, manual switch statements |
| State | Each webview redundantly fetches and maintains repo/context/account/subscription slices |
| Embedding | No first-class way to reuse complex UI flows inside another webview |
| Performance | Chatty full-state syncs; repeated initial hydration for each webview |
| Serialization | Manual handling of Dates/undefined; inconsistent handling of optional fields |
| Debugging | Hard to trace message causality and request lifecycle |
| Evolution | Adding/removing a field requires synchronized manual edits |

## 5. Requirements

### Functional
* F1: Register micro-apps with unique IDs and mount them dynamically within a host webview container.
* F2: Provide an API for a parent micro-app to request mounting a child micro-app into a DOM slot.
* F3: Shared global store accessible to all mounted micro-apps with selective subscriptions.
* F4: Support message patterns: request/response (async), fire-and-forget events, server push events, streaming (chunked responses), and cancelation.
* F5: Capability/version negotiation per connection.
* F6: Type-safe command invocation and responses (compile-time + runtime validation).
* F7: De/serialization for structured data (Date, Map/Set via normalized forms) within JSON boundaries.
* F8: Support isolating micro-app local state (internal UI) while sharing read-only or read/write global slices.
* F9: Graceful degradation if a micro-app (child) fails to mount (error boundary).
* F10: Hot reload friendliness in `watch` mode (dev tooling reloads only changed micro-app bundle).
* F11: Custom Element contract – defining `<gitlens-app-*>` elements that self-register & auto-handshake when connected.
* F12: Zero manual host code required beyond importing the element bundle and placing the tag in DOM.
* F13: Attributes & properties drive initial params; mutation observed → dispatch typed update action.
* F14: Support lazy upgrade (element tag may exist before class defined; element hydrates once definition loaded).
* F15: Nested custom elements (app inside app) coordinate subscriptions without duplication.

### Non-Functional
* NF1: Average message payload size reduced ≥30% vs current (hydration scenarios).
* NF2: P99 request latency overhead for routing/validation < 2ms (measured inside webview).
* NF3: Memory overhead for framework scaffolding < 150 KB gzipped shared runtime.
* NF4: Backward compatibility layer for at least two release cycles.
* NF5: Strict CSP and no `eval`/Function constructors.
* NF6: Testable in both desktop and web (vscode.dev) environments.

## 6. High-Level Architecture

```
┌──────────────────────────── VS Code Webview (Single Shell) ────────────────────────────┐
│  Shared Runtime (Bridge + Store + Registry + Serialization + Logger)                  │
│                                                                                       │
│  Micro-App Containers:                                                                │
│  ┌──────────────┐  ┌──────────────┐  ┌────────────────┐                               │
│  │ Graph (host) │  │ Composer     │  │ Home           │  ...                          │
│  └──────┬───────┘  └─────┬────────┘  └──────┬─────────┘                               │
│         │ Mount Request        │                 │                                      │
│         ▼                      ▼                 ▼                                      │
│     Embedding API <───────── Registry / Loader ────────────────────────┐               │
│                                                                         │              │
│                      Typed IPC Bridge (Channel Router)                  │              │
│                                                                         │              │
│  Global Store (Authoritative in Extension Host) <—— diff/patch ——> Webview Cache       │
└───────────────────────────────────────────────────────────────────────────────────────┘

Extension Host Side:
  * Host Bridge Adapter (one per VS Code webview instance)
  * Global Store Authoritative State Manager
  * Module Capability Registry
  * IPC Router & Handlers
```

### Key Components

| Component | Responsibility |
|-----------|----------------|
| Bridge | Transport abstraction over `postMessage` (host ↔ guest) with queuing, batching, tracing |
| Protocol Registry | Defines message schemas (zod or ts-json-schema) and version mapping |
| Codegen | Generates TypeScript types + discriminated unions + runtime validators + IPC client stubs |
| Global Store | Authoritative extension-host state; pushes structured patches to webview shell |
| Webview Cache | Browser-side normalized store (read-optimized) with subscription diff emission |
| Embedding API | Mount/unmount, lifecycle events, focus management, disposal, error boundaries |
| Micro-App Loader | Dynamic import + dependency injection + handshake negotiation |
| Serialization | Deterministic encode/decode (handles Dates, BigInt, Maps, Sets) |
| Logger/Tracer | Structured logs, correlation IDs, performance marks |
| Compatibility Shim | Bridges legacy IPC consumers to new protocol during migration |
| (Optional) Registry | Central manifest for prefetch/licensing; omitted in lean profile |

### 6.1 Layering

```
┌────────────────────────────── UI (Micro-Apps) ───────────────────────────────┐
│ Lit components, local ephemeral state, render logic                          │
├───────────────────── Embedding & App Runtime Layer ─────────────────────────┤
│ Mount/Unmount API • Capability Registry • Focus Mgmt • Error Boundaries      │
├──────────────────────── State Sync & Cache Layer ───────────────────────────┤
│ Slice Subscription Manager • Patch Applier • Optimistic Update Reconciler    │
├──────────────────────────── IPC Client Layer ───────────────────────────────┤
│ Generated Proxies • Streaming Helpers • Cancelation Tokens                   │
├──────────────────────────── Protocol Layer ─────────────────────────────────┤
│ Schema Validators • Version Negotiation • Envelope Normalization            │
├──────────────────────────── Transport Layer ────────────────────────────────┤
│ Bridge (postMessage) • Batching • Backpressure • Tracing IDs                │
├────────────────────────── Extension Host Core ─────────────────────────────┤
│ Authoritative Store • Handlers • Patch Derivation • Security Guards         │
└──────────────────────────────── Platform (VS Code) ─────────────────────────┘
```

Design principle: Each layer only depends downward (UI -> Embedding -> State -> IPC Client -> Protocol -> Transport). Testing strategy mirrors layers for isolation.

### 6.2 Transparent Embedding via Custom Elements

Objective: A micro-app can be delivered purely as a Custom Element (e.g., `<gitlens-app-composer>`). Placing the tag inside any host webview (Graph, Home, future composite shells) instantiates the app with zero imperative bootstrap code.

Key Concepts (Lean Model – No Mandatory Central Registry):
* Declarative Mount: DOM insertion triggers `connectedCallback` → self-registration → handshake.
* Attribute/Property Mapping: Initial parameters provided as attributes (kebab-case) or properties (camelCase). Observed attributes map to typed params with runtime validation.
* Auto Store Subscription: App definition declares required slices; on connect the element requests them. On disconnect, reference counts decremented.
* Shadow DOM Boundary: Each app uses shadow root for style encapsulation while inheriting global design tokens via CSS variables from host root.
* Lifecycle Hooks: `onParamsChanged`, `onSlicesReady`, `onFocus`, `onDispose` invoked by runtime adapter.
* Nested Transparency: If an app nests another app element, the child skips creating a duplicate bridge instance; it reuses the shell-level single bridge via a lightweight context lookup.
* Optional Registry: A centralized manifest/registry can be added later for prefetching, licensing, or slice batching optimizations; initial implementation omits it for simplicity.

Custom Element Contract (simplified TypeScript – lean version):
```ts
interface AppElementParams<TParams> {
  params: Partial<TParams>;              // Derived from attributes/properties
  setParams(newParams: Partial<TParams>): void; // Emits param update action
}

type MicroAppLifecycle<TParams = unknown> = {
  init(ctx: AppContext, el: HTMLElement, params: TParams): Promise<void> | void;
  update?(params: TParams): void;
  dispose?(): void;
};

// Static fields / metadata are placed on the element class itself instead of a registry record.
// Example (conceptual):
// class GitLensAppComposer extends HTMLElement { static requiredSlices = ['composerDrafts']; }
```

Runtime Steps (Lean):
1. Element class static initializer registers definition with central registry.
2. `connectedCallback`:
  * Resolves bridge/context (global accessor).
  * Validates & normalizes params (if schema present).
  * Dispatches `gitlens-app-mount` event (shell listener aggregates required slices if implemented).
  * Invokes `lifecycle.init`.
  * Marks element with `data-app-ready` attribute & emits `gitlens-app-ready`.
3. Attribute change → debounced param validation → `lifecycle.update` with merged params.
4. `disconnectedCallback`: If not merely moved (`isConnected` check), invokes `dispose`, unsubscribes slices (ref decrement), cleans param observers.

Param Handling:
* Attribute sources are strings → parsed via schema (numbers, booleans, JSON fragments if `data-*` prefix used).
* Properties set before definition upgrade queued (standard custom elements pattern) and replayed post-definition.

Error Handling:
* Validation failure adds `data-app-error` + dispatches custom DOM event `gitlens-app-error` with details.
* Fatal errors during init show a lightweight fallback slot with retry button triggering `reinit()` method.

Focus & Accessibility:
* Element exposes optional `focusTargetSelector` static; runtime focuses that node after `init` if `autofocus` attribute present.
* ARIA role inheritance: host may set `role="complementary"` or `role="region"`; element will not override but can add `aria-label` if missing.

Seamless Embedding Example:
```html
<!-- Inside Graph webview HTML template -->
<gitlens-app-composer draft-id="123" autofocus></gitlens-app-composer>
```

No additional imperative JavaScript required besides ensuring the composer bundle (registering the custom element) has been imported.

### 6.3 Lit Integration & Conventions
All app elements MUST extend `LitElement` to align with existing shared component ecosystem.

Guidelines:
* Reactive `params` object exposed as a non-attribute property (no serialization overhead for complex params).
* Individual frequently changing primitives (e.g., `draftId`) may be exposed as separate reactive properties when beneficial for fine-grained updates.
* Shadow DOM enabled; global design tokens consumed via CSS custom properties.
* Avoid heavy state duplication: derive transient UI state from shared slices + params; keep ephemeral UI-only state local.
* Use `requestUpdate()` sparingly—prefer Lit reactive property semantics.
* Event Emission: use `gitlens-app-*` lifecycle events plus domain-specific events under `gitlens-<app>-*` namespace.
* Accessibility: each root element adds `role="region"` and `aria-label` if not provided by host. Modal-style embeddings set `role="dialog"` with proper focus trapping handled by host shell utilities.

Performance Notes:
* Avoid deep property mutation inside `params`; when necessary, reassign a new object to trigger Lit change detection.
* Defer expensive rendering until required slices are marked ready (element can show skeleton state otherwise).
* Use `@state` for internal ephemeral fields not part of public API.

## 7. IPC Protocol Design
### 7.0 Transitional IPC (Pre-Protocol V2)
Before full adoption of the new typed protocol, introduce a centralized legacy wrapper to reduce surface area:

API Shape:
```ts
interface LegacyIpc {
  request<T = unknown>(channel: string, action: string, params?: unknown, options?: { timeoutMs?: number }): Promise<T>;
  fire(channel: string, action: string, payload?: unknown): void; // fire-and-forget
  on<T = unknown>(channel: string, event: string, handler: (data: T) => void): () => void;
}
```

Implementation Notes:
* Single listener bound to `window.addEventListener('message')` performs dispatch based on `{ channel, action, type }` shape.
* All existing disparate messaging utilities funnel through this wrapper.
* Provides logging instrumentation to establish baseline metrics prior to migration.
* Wrapper internally tags each message with `_trace` id to correlate request/response for later upgrade.

Decommission Criteria:
* ≥90% of high-frequency channels migrated to protocol V2 generated clients.
* Remaining legacy paths isolated to low-volume or deprecated features slated for removal.


### 7.1 Message Envelope
```ts
interface Envelope<V extends string = string> {
  id?: string;              // Present for request/response & streaming frames
  t: 'req' | 'res' | 'evt' | 'str';
  ch: string;               // Channel / namespace (e.g., 'store', 'repo', 'composer')
  a?: string;               // Action / method key for requests
  v: V;                     // Protocol version tag (e.g., '1', '1.1')
  p?: unknown;              // Payload (request params or event data)
  e?: SerializedError;      // Error object for failed responses
  s?: StreamFrame;          // Streaming meta (see below)
  meta?: Meta;              // Tracing, timing, diagnostics
}
```

### 7.2 Request/Response
* Client sends `t='req'`, `id` + `a`.
* Host responds with `t='res'`, same `id`, `p` or `e`.
* Timeout & cancelation tokens supported via separate `cancel` event on channel.

### 7.3 Events (Fire-and-Forget)
* `t='evt'`, no `id`; processed best-effort.

### 7.4 Streaming
* `t='str'` frames with `s.kind: 'start' | 'chunk' | 'end' | 'error'`.
* Optional backpressure via window size (client sends `ack` count).

### 7.5 Batching
* Envelope array allowed when sequential frames emitted within same animation frame; host decompresses sequentially.
* Heuristic: collect frames for up to 4ms or 16 messages, whichever first, then flush.
* Dev tooling can disable batching via debug flag for easier inspection.

Example batched payload:
```json
[
  {"t":"evt","ch":"store","a":"patch","p":{...},"v":"2"},
  {"t":"res","id":"42","ch":"composer","v":"2","p":{"ok":true}}
]
```

### 7.6 Version Negotiation
1. On handshake, client lists supported protocol versions per channel.
2. Host returns chosen versions + feature flags.
3. Fallback: if mismatch, host offers downgrade or instructs reload with minimum version message.

### 7.7 Type Safety
* Source-of-truth schemas in `src/protocol/definitions/*.ts` using zod (or lightweight custom schema to reduce bundle size).
* Codegen script (`pnpm run generate:protocol`) outputs:
  * `protocol.generated.ts` – TS types + validators
  * `client.generated.ts` – IPC proxy functions (e.g., `composer.openDraft(id)`)
  * Discriminated union for all envelopes for exhaustive switch checks.

### 7.8 Error Model
```ts
interface SerializedError { code: string; message: string; details?: unknown; traceId?: string; }
```
* Standard codes: `InvalidPayload`, `Timeout`, `NotFound`, `Conflict`, `Unauthorized`, `Internal`.
* All unexpected errors mapped to `Internal` with sanitized message in prod builds.

### 7.9 Security
### 7.10 Cancelation & Timeouts
* Requests optionally include `meta.cancelToken` (UUID); client can send `evt` `{ a: 'cancel', p: { id, cancelToken } }`.
* Host propagates cancel to underlying async operation if supported; responds with `res` error `{ code: 'Canceled' }`.
* Default request timeout (configurable, e.g., 15s) results in synthetic timeout error if no response—client cleans up listeners.

### 7.11 Tracing & Diagnostics
* `meta.traceId` (UUID) propagated across request → response → emitted events triggered by that action.
* `meta.parentTraceId` supports causal chaining (e.g., saveDraft triggers patch events referencing parent trace).
* Optional `meta.perf` array of phase timings (host can enrich before responding).

* Channel allowlist verified host-side; unknown `ch` rejected silently or with `Unauthorized` (configurable).
* Payload validation before handler invocation; reject on first error.
* Size limits (e.g., 512KB per envelope) to guard memory pressure.

## 8. Serialization Strategy

Constraints: Must remain `JSON.stringify` compatible. Approach: Preprocess values with structured replacer + reviver.

| Type | Encoding |
|------|----------|
| Date | `{__t:'d', v:number}` (ms epoch) |
| BigInt | `{__t:'bi', v:string}` |
| Map | `{__t:'m', v:[ [k,v], ... ]}` (keys recursively encoded) |
| Set | `{__t:'s', v:[ ... ]}` |
| Undefined | Omit field (schema default handles) |
| Enum | Plain string literal |
| Discriminated unions | Include `kind` or existing discriminator |

Round-trip via lightweight encoder/decoder. Future optimization: binary packer (NOT in scope now).

## 9. Shared State Model

### 9.1 Authoritative Host Store
* Single store in extension host (modular reducers/services) exposes versioned slices: e.g., `repos`, `branches`, `user`, `subscription`, `graphViewport`, `composerDrafts`.

### 9.2 Webview Cache
* Browser-side mirror holds latest known slice versions.
* Each slice has metadata: `{version: number, ts: number}`.

### 9.3 Subscription API
```ts
store.subscribe(['repos', 'user'], (patch) => { /* apply */ });
```
* Host tracks per-connection subscription sets to minimize broadcast.

### 9.4 Diff/Patch Protocol
```ts
interface SlicePatch<T> { slice: string; v: number; op: 'replace' | 'merge' | 'delta'; data: Partial<T> | JsonPatch[] | T }
```
* Default: `merge` shallow for simple objects; `delta` (JSON Patch RFC6902) for large nested data (e.g., commit graph nodes page additions). Host chooses strategy per slice.

### 9.5 Hydration
* Initial handshake includes minimal bootstrap (critical user + subscription + feature flags) then progressive hydration events for heavier slices (graph, repo metadata) lazy-loaded when micro-app requests them.

### 9.6 Write Flow
* Micro-app calls typed action (e.g., `composer.saveDraft`), host validates, mutates authoritative state, emits resulting patch events.
* Optimistic updates allowed if schema marks action as `optimistic: true`; rollback event if failure.

Optimistic rollback example:
```json
{ "t":"evt", "ch":"store", "a":"patch", "p":{"slices":[{"slice":"composerDrafts","v":13,"op":"merge","data":{"abc":{"content":"New text (optimistic)"}}}]}, "v":"2", "meta":{"traceId":"t1","optimistic":true}}
```
Failure response:
```json
{ "t":"res","id":"99","ch":"composer","v":"2","e":{"code":"Conflict","message":"Draft outdated"},"meta":{"traceId":"t1"}}
```
Rollback event:
```json
{ "t":"evt","ch":"store","a":"patch","p":{"slices":[{"slice":"composerDrafts","v":14,"op":"merge","data":{"abc":{"content":"Prev stable"}}}]}, "v":"2", "meta":{"traceId":"t1","rollback":true}}
```

### 9.7 Conflict Resolution
* Last-write-wins with version numbers; future CRDT extension possible (design to avoid blocking later adoption).

## 10. Embedding Model

### 10.1 Concept
* A *micro-app* is a self-contained Lit root with an App Definition: ID, capabilities, required slices, optional slices, lifecycle hooks, provided services.

```ts
interface MicroAppDefinition {
  id: string;
  version: string;
  capabilities: string[]; // e.g. ['compose', 'render:graph:metrics']
  requiredSlices: string[];
  optionalSlices?: string[];
  mount(container: HTMLElement, ctx: AppContext): Promise<AppInstance>;
}
```

### 10.2 Mount Lifecycle
1. Host (graph app) calls `embedding.mount('composer', { container, params })`.
2. Registry checks if loaded; if not, dynamic imports bundle `micro-app-composer.[hash].js`.
3. Handshake: composer declares additional slice subscriptions.
4. Store ensures those slices hydrated (fetch + patches) before `ready` event.

### 10.3 Unmount
* `instance.dispose()` triggers subscription cleanup, pending request cancelation, DOM removal.

### 10.4 Focus & Accessibility
* Focus manager notifies micro-app when container gains focus; micro-app returns preferred initial focus selector.
* ARIA landmarks ensure nested micro-apps do not break accessibility tree.

### 10.5 Error Boundaries
* Loader wraps mount in try/catch; on error renders fallback (configurable) with retry.

### 10.6 Styling & Theming
* Shared design tokens in root `<style>`; each micro-app receives CSS variables; avoids iframe boundaries while allowing shadow DOM encapsulation.

### 10.7 Communication Between Sibling Micro-Apps
* Indirect only via global store or explicit typed actions (no direct object references) to maintain isolation.

### 10.8 Coordinated Subscriptions (Multi-App)
* When multiple embedded apps request overlapping slices, subscription manager de-duplicates host subscription, tracking reference counts.
* Unmount decrements counts; slice unsubscribed only when count reaches zero and no other app depends on it.
* Large slices (e.g., `graphViewport`) can support scoped subset filters per app; host merges filters (union) and emits filtered patches with per-app gating performed client-side if necessary.

## 11. Type Generation Workflow

1. Define schema modules under `src/protocol/definitions/`.
2. Run `pnpm run generate:protocol`.
3. Generated outputs (committed):
   * `src/protocol/protocol.generated.ts`
   * `src/protocol/client.generated.ts`
   * `src/protocol/schema.json` (for docs / telemetry alignment)
4. ESLint rule forbids importing definition files directly inside runtime logic (must use generated types).

## 12. Performance Considerations

| Concern | Strategy |
|---------|----------|
| Startup cost | Progressive hydration; code-split micro-app bundles |
| Message volume | Patch diffs, batching, subscription filtering |
| Render thrash | Coalesce sequential patches into animation frame commit |
| Large graph data | Stream pages via `stream` frames; host builds indexes incrementally |
| Serialization overhead | Fast path primitive check; pool object allocators |
| Dev reload | HMR boundary at micro-app entry; stable bridge instance |

Metrics captured via tracing IDs (`traceId`) and performance marks (e.g., `ipc:req:<action>`).

## 12.1 Implementation Profiles
Two supported deployment profiles:

| Profile | Included Components | Primary Use Case | Trade-offs |
|---------|--------------------|------------------|------------|
| Lean | Bridge, Protocol, Lit custom elements, Shared Store (no registry) | Fast adoption & minimal code | Less centralized optimization; per-element mount events |
| Enhanced | Lean + Optional Registry + Prefetch Manifest + Slice Aggregator | Performance tuning for heavy multi-embed pages | Slightly higher complexity & bundle size |

Profile Selection Criteria:
* Start with Lean for all new conversions.
* Introduce Enhanced only if telemetry shows hydration duplication >15% overhead or licensing/prefetch needs emerge.
* Keep code paths swappable via build-time flag (`process.env.GITLENS_WEBVIEW_PROFILE`).

## 13. Security & Hardening
* All incoming messages validated before dispatch.
* Deny unknown action/channel; log once per action (rate-limited) to avoid noise.
* Size & rate limits (configurable): e.g., 200 msgs / 5s sliding window per connection before soft warning, 500 for hard throttle.
* Sanitized errors; detailed stack only in dev mode (`process.env.NODE_ENV !== 'production'`).
* No dynamic `eval`; dynamic imports only from hashed manifest map.

## 14. Migration Plan

### Phase -1: Factoring & Preparation (1–2 sprints)
Objective: Restructure target webviews (Home, Commit Composer) so their primary UI can be lifted into a single Lit custom element without legacy bootstrap coupling.

Scope:
* Isolate root view component (e.g., `HomeAppRoot`, `CommitComposerAppRoot`).
* Replace imperative hydration bootstrap with internal data request hooks.
* Introduce a thin `requestData()` abstraction returning promises for required slices / domain data.
* Centralize ad-hoc `window.acquireVsCodeApi().postMessage` usage behind a transitional IPC helper (`legacyIpc.request(channel, action, params)`).
* Remove side-effect global state writes during initialization (convert to explicit async flows inside component lifecycle / Lit `firstUpdated`).

Exit Criteria:
* Root component can be instantiated in isolation in a test harness with mocked IPC provider.
* No direct calls to legacy message passing outside the centralized helper.
* All initial data loads originate from component lifecycle (not external bootstrap script order).
* Telemetry baseline captured: initial payload bytes, time to interactive, number of distinct IPC calls during first render.

### Phase 0: Foundations (1 sprint)
* Implement core bridge + envelope + logger (feature-flagged behind `gitlens.experimental.ipcV2`).
* Add codegen pipeline with one sample channel.
* Telemetry: begin emitting `ipcVersionUsed` and `ipcMessageCount` for baseline legacy usage.

### Phase 1: Store & Protocol (2 sprints)
* Implement host authoritative store + slice patching.
* Convert one low-risk webview (e.g., Home) to new system (standalone, not embedded yet).
* Gate by progressive rollout: 5% → 25% → 50% → 100% based on error rate (<0.5% failed envelopes) and performance KPIs.

### Phase 2: Embedding Primitive (2 sprints)
* Implement registry + dynamic loader.
* Convert Commit Composer to micro-app; allow embedding into Home (test scenario).
* Telemetry: `microAppMountTime`, `microAppFailureCount`.

### Phase 3: Graph Integration (2–3 sprints)
* Refactor Graph to be host micro-app shell.
* Embed Composer + Home surfaces (e.g., collapsible side panels or modal overlay).
* Dark launch embedding behind `gitlens.experimental.embedApps` until stability confirmed.

### Phase 4: Broader Adoption (ongoing)
* Migrate remaining webviews (Settings, Launchpad, etc.).
* Deprecate old IPC path; telemetry monitors residual usage.
* Weekly report: legacy vs new message ratio, patch average size delta, error codes distribution.

### Phase 5: Removal
### 14.1 Webview → Custom Element Migration Steps
### 14.2 Factoring Guidelines (Home & Commit Composer)
Refactor goals: determinism, isolation, composability.

1. Single Entry Component: Ensure exactly one exported Lit root (no conditional multiple roots). Name pattern: `<Feature>NameAppRoot`.
2. Stateless Shell: Strip global mutable singletons; inject dependencies via context/provider or thin props.
3. Data Acquisition Pattern:
  * Define `requiredData: DataToken[]` static on root (e.g., `['user','repos','drafts']`).
  * On `connectedCallback/firstUpdated`, iterate tokens, call `requestData(token)` returning a promise; aggregate with `Promise.allSettled`.
  * Render skeleton until all required promises resolved (or timeout with partial data handling strategy).
4. IPC Wrapper: Replace raw `postMessage` with `legacyIpc.request<TResponse>(channel, action, params)` returning typed promise; events with `legacyIpc.on(channel, event, handler)`.
5. Side Effects: Move analytics/logging into dedicated effect modules invoked after data readiness.
6. Cancellation: For long-running requests (AI generation, diff stats) pass a cancel token from component-owned controller.
7. Error Surfacing: Normalize all request failures to `{ code, message, retry?: () => void }` shape for consistent UI handling.
8. Strict Rendering Boundary: Avoid root component reading from global `window` state except `acquireVsCodeApi` (abstracted) and feature flags.
9. Param Mapping: Derive initial params from host-provided `data-*` attributes or injected global config object, then freeze that object to prevent mutation.
10. Testing Harness: Provide a story/demo module that mounts the root with stubbed `legacyIpc` returning fixture data (verifies isolation).

Anti-Patterns to Remove:
* Implicit sequential bootstrap ordering dependencies (script A must run before B to set globals).
* Hidden state mutation during module evaluation time.
* Multiple overlapping initial fetches for identical data in sibling components.

For each existing webview (e.g., Commit Composer):
1. Identify root Lit component currently bootstrapped imperatively.
2. Convert root to extend `LitElement` if not already.
3. Expose a single custom element tag (e.g., `<gitlens-app-composer>`); avoid wrapper registries (Lean profile).
4. Encapsulate initialization inside `init` lifecycle (or first `updated` hook if no async preconditions) and emit lifecycle events.
5. Replace direct host calls with generated IPC client proxies (or interim shim) invoked from lifecycle.
6. Map launch parameters to properties (preferred) or attributes (only for primitive string/number/boolean); aggregate extras into a `params` property.
7. Feature flag gating: element early-exits and renders fallback if profile not enabled.
8. Dogfood: embed element inside Graph while still supporting standalone panel instantiation (legacy bootstrap loads the element and sets properties instead of imperative render).
9. Telemetry: record `mountTime`, `readyTime`, `hydrationBytes`, `errorCount` for old vs new.
10. Evaluate need for Enhanced profile (registry) only if telemetry thresholds exceeded.
11. Remove legacy bootstrap path after two stable releases with <5% fallback usage.

* After ≥2 releases with <5% old IPC usage, remove legacy code.

## 15. Backward Compatibility
* Shim layer: old webviews still load legacy script which internally maps legacy messages to new protocol if host flag enabled.
* Dual-publish of critical slices (legacy + new) until each consumer migrated.
* Telemetry fields: `ipcVersionUsed`, `microAppsEmbedded`, `patchAvgSize`.

## 16. Risks & Mitigations
| Risk | Impact | Mitigation |
|------|--------|-----------|
| Over-engineering early | Delays | Start minimal (no streaming) then extend |
| Bundle size growth | Performance | Shared runtime modular; tree-shake validators |
| Race conditions w/ hydration | Inconsistent UI | Version gating + queue actions until required slices ready |
| Telemetry drift | Data quality | Generate schema.json; validate events pre-send |
| Developer misuse (bypassing store) | State divergence | Lint rule forbids direct mutation; read-only proxies |
| Web (vscode.dev) limitations | Feature regression | Test in both targets CI; avoid Node-only APIs in shared runtime |

## 17. Testing Strategy
* Unit: schema validators, serialization round-trips, patch merge logic.
* Integration: mount/unmount lifecycle; diff application idempotency.
* E2E: Graph embedding Composer; optimistic update rollback.
* Load tests (synthetic): simulate 10K node graph patch streaming.
* Telemetry assertions: ensure events fire with correct protocol version.

## 18. Tooling & Developer Experience
* VS Code problem matcher for generator staleness (fail build if definitions newer than generated output).
* Dev panel overlay (toggle via query param `?debugIpc=1`) listing live messages + timings.
* CLI script: `pnpm run protocol:check` verifies no breaking changes without version bump.

## 19. Acceptance Criteria (MVP)
1. Embed Commit Composer inside Graph with shared user + repo slices (no full re-fetch).
2. Actions invoked from embedded Composer (e.g., generate commit message) succeed via new IPC.
3. Message schemas enforced at runtime—invalid payload logged + rejected.
4. Patch-based updates reduce initial Graph+Composer hydration payload size by ≥30% vs baseline measurement.
5. Telemetry reports `ipcVersion=2` for >95% of relevant interactions in dogfood.
6. Legacy IPC path still functional for non-migrated webviews with <5% error rate and clearly reported usage counts.

## 20. Open Questions
* Do we want to predeclare micro-app dependency graph for prefetching bundles?
* Should we adopt a CRDT library now for future collaborative editing? (Likely no; design leaves room.)
* Need richer streaming? (Only if large AI response payloads exceed batch limits.)

## 21. Appendix

### 21.0 Rationale: Lean Lit-Only vs Registry
Decision: Start Lean (no mandatory registry). Motivation:
* Simplicity: Direct developer ergonomics—define a Lit element, import, use.
* Lower Initial Risk: Fewer moving parts to debug in early rollout.
* Incremental Optimization: Registry gains (prefetch, aggregated slice negotiation) become valuable only after multiple heavy concurrent embeds; we defer until telemetry justifies.
* Familiar Patterns: Aligns with existing component authoring in the repo (Lit components, property-driven reactivity).

Trade-offs Accepted:
* Slightly more per-element ceremony for declaring required slices (handled via events or static fields later).
* Potential duplicate hydration requests early—mitigated by simple de-dupe in host once store layer implemented.

Exit Criteria to Introduce Registry:
* Hydration duplication >15% sustained across dogfood sessions.
* Need for license-gated code splitting before element instantiation.
* Prefetch wins ( >50ms saved in median mount time) demonstrated in prototype.

This staged approach keeps scope controlled while retaining a path to richer orchestration when warranted.

### 21.5 Factoring Readiness Checklist
| Item | Pass Criteria | Metric / Evidence |
|------|---------------|-------------------|
| Single Root Component | One exported Lit root symbol | Static analysis / build report |
| Centralized IPC | All `postMessage` usages routed via `legacyIpc` | Grep shows zero raw calls outside helper |
| Data Pull Model | Root initiates all required data requests on mount | Code review + test harness |
| No Global Mutation on Import | Side effects only inside lifecycle methods | ESLint rule / static scan |
| Retry & Error Normalization | Unified error objects rendered | Visual & unit tests |
| Cancellable Long Ops | AbortController used & verified | Test harness simulation |
| Telemetry Baseline Collected | `initialHydrationBytes`, `timeToReady`, `ipcCallCount` captured | Telemetry dashboard snapshot |
| Fixture Harness Exists | Story/demo loads with mock IPC | Manual run screenshot / CI check |

All checklist items must pass (or have a documented exception) before starting Phase 0 for that webview.

### 21.1 Example Protocol Definition (Source)
```ts
// src/protocol/definitions/composer.ts
export const composerProtocol = defineChannel('composer', {
  openDraft: method({ params: z.object({ id: z.string() }), result: z.object({ draft: draftSchema }) }),
  saveDraft: method({ params: z.object({ id: z.string(), content: z.string().min(1) }), result: z.object({ version: z.number() }), optimistic: true }),
  generateMessage: stream({ params: z.object({ diffStats: z.any() }), chunk: z.object({ text: z.string() }), done: z.object({ full: z.string() }) }),
  onDraftUpdated: event({ payload: z.object({ id: z.string(), patch: z.any() }) }),
});
```

### 21.2 Generated Client Snippet
```ts
client.composer.openDraft({ id: 'abc' }).then(r => r.draft);
const stream = client.composer.generateMessage({ diffStats });
for await (const c of stream) { /* append c.text */ }
```

### 21.3 Patch Event Example
```json
{
  "t": "evt",
  "ch": "store",
  "a": "patch",
  "p": { "slices": [ { "slice": "composerDrafts", "v": 12, "op": "merge", "data": { "abc": { "content": "Updated" } } } ] },
  "v": "2"
}
```

### 21.4 Embedding Call (Graph Micro-App)
```ts
await embedding.mount('composer', { container: el, params: { draftId } });
```

---

End of Spec v0.1
