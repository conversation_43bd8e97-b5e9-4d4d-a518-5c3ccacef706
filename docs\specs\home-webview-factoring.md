# Home Webview Factoring Spec

Status: Draft (v0.1)
Owner: (TBD)
Reviewers: Webviews, Core, Product
Target Window: Phase -1 (Factoring) – 1 to 1.5 sprints

## 1. Context & Current State (High-Level)
The Home webview aggregates multiple concerns (welcome content, repo status, onboarding actions, subscription nudges, promotional panels) with ad-hoc IPC calls and implicit bootstrap ordering. Direct `postMessage` usages are scattered, and initial rendering depends on sequential script evaluation rather than a single root Lit component making declarative data requests.

Pain Indicators (qualitative):
* Duplicate or overlapping data fetches (e.g., user/subscription state re-requested by nested components).
* Challenging to embed Home sections elsewhere (tight coupling to bootstrap and global objects).
* Hard to test in isolation without full extension host context.
* Mixed responsibility (initialization logic interleaved with UI composition).

## 2. Objectives
1. Provide a single embeddable Lit root (`<gitlens-app-home>`) capable of standalone or embedded usage.
2. Transition to a pull-based data model: root requests required slices; child components receive reactive inputs only.
3. Centralize IPC behind `legacyIpc` wrapper (pre-V2 protocol) to eliminate direct `postMessage` calls.
4. Normalize loading states and skeletons; ensure consistent fallback when partial data missing.
5. Reduce first-render IPC call count by ≥25% via batching / de-duplication.
6. Establish deterministic mount order independent of script injection order.
7. Introduce cancellation for long operations (e.g., computing repo insights) to avoid wasted work on fast navigation.
8. Collect baseline & post-refactor metrics to quantify improvement.

## 3. Non-Goals
* Implementing final IPC V2 (handled in later phases).
* Redesigning UI/visual layout (cosmetic changes minimized). 
* Adding new feature content to Home (feature freeze during factoring).
* Registry/prefetch optimization (Lean profile only).

## 4. Target Architecture Overview
```
<gitlens-app-home>
  ├─ HeaderBar (static, minimal state)
  ├─ QuickActionsPanel (props: { repoState, user })
  ├─ RepoSummaryCard (props: { repos, activeRepo, subscription })
  ├─ RecentActivitySection (lazy load after initial ready)
  ├─ PromotionsSlot (feature-flagged, subscription-aware)
  └─ FooterLinks
```

Data Flow:
* Root triggers `requestData()` for tokens: `['user','subscription','repos','activeRepo','recentActivity?lazy']`.
* Each token resolved via `legacyIpc.request('homeData','get', { token })` or specialized channel.
* Lazy tokens (suffix `?lazy`) fetched after initial ready event (microtask or intersection observer if off-screen).

## 5. Data & IPC Plan
| Token | Purpose | Blocking? | Source Channel/Action | Notes |
|-------|---------|-----------|------------------------|-------|
| user | Personalization, gating | Yes | account/getUser | Minimal payload, prime early |
| subscription | Feature gating | Yes | subscription/getStatus | Combined with user to reduce calls? (future) |
| repos | Repo list for quick actions | Yes | repos/list | Paginate > N repos deferred |
| activeRepo | Current repo metadata | Yes | repos/active | Could piggyback on repos response |
| recentActivity | Activity feed | No (lazy) | activity/listRecent | Streaming candidate (Phase 0+) |
| promotions | Feature upsell/promo | No | marketing/getPromotions | Feature-flag guard |

IPC Wrapper Requirements:
* Promise-based request with timeout (default 10s) and cancellation support.
* Batched multi-token request optimization: root may call `legacyIpc.request('homeData','batch',{ tokens:[...] })` if available, fallback to parallel individual calls.
* Logging: capture `traceId`, `durationMs`, `payloadBytes` (computed via length of JSON string once) for baseline telemetry.

Cancellation Strategy:
* Use `AbortController` per request set; abort all outstanding non-blocking tokens if component disconnects before completion.
* Blocking tokens proceed unless component fully disposed (rare during initial load).

Error Handling:
* Transform errors into `{ code, message, retriable }` objects; show inline retry button for retriable user/network failures.
* Non-blocking token failure logs diagnostic event but does not block `ready` lifecycle.

## 6. Component Responsibilities
| Component | Responsibility | Receives | Emits |
|-----------|---------------|----------|-------|
| HomeRoot | Orchestrates data acquisition & layout | (none) | lifecycle events, metrics events |
| QuickActionsPanel | Action shortcuts contextual to repo/user | user, activeRepo | action events |
| RepoSummaryCard | Summarizes active + counts | repos, activeRepo, subscription | navigation events |
| RecentActivitySection | List of recent commits/PR/issues | recentActivity | pagination events |
| PromotionsSlot | Conditional promos | subscription, user, promotions | promoViewed |

## 7. Telemetry & Metrics
Baseline Fields:
* `home.initialHydrationBytes`
* `home.timeToReadyMs`
* `home.ipcCallsInitial`
* `home.failedTokenCount`
* `home.lazyTokenDelayMs` (time from ready → lazy token resolved)

Targets (post-refactor):
* `timeToReadyMs`: -15% vs baseline
* `ipcCallsInitial`: -25% (merge or batch user+subscription+repos where feasible)
* `initialHydrationBytes`: -10% (avoid redundant fields in separate calls)
* `failedTokenCount`: ≤ baseline (no increase)

Instrumentation Plan:
* Root captures timestamps at: construct, firstUpdated, allBlockingResolved, ready event.
* Wrapper logs each request; root aggregates counts & bytes into a single telemetry event `home.ready`.

## 8. Phased Task Breakdown
| Phase | Tasks | Est |
|-------|-------|-----|
| 1 | Introduce `legacyIpc` wrapper & migrate Home raw calls | 0.5d |
| 1 | Identify & document tokens + implement requestData helpers | 0.5d |
| 2 | Create `HomeRoot` Lit element & move layout markup | 1d |
| 2 | Wire blocking token orchestration + skeleton states | 1d |
| 3 | Implement lazy token loading + cancellation | 0.5d |
| 3 | Add telemetry aggregation & event emission | 0.5d |
| 4 | Refactor child panels to pure presentational components | 1d |
| 4 | Remove legacy bootstrap script logic | 0.5d |
| 5 | Baseline vs post metrics comparison & tuning | 0.5d |

## 9. Acceptance Criteria
1. `<gitlens-app-home>` mounts declaratively and reaches `ready` state without external imperative bootstrap.
2. All legacy `postMessage` usages in Home replaced by `legacyIpc` calls (grep zero raw occurrences in Home scope).
3. Blocking tokens resolved before `ready` event; lazy tokens fetched after without blocking initial interaction.
4. Telemetry event `home.ready` includes required metrics and appears in QA builds.
5. Root component test harness renders with mocked IPC (no extension host needed) producing stable snapshot.
6. Initial IPC call count reduced ≥25% (or documented exception if not achievable) vs baseline measurement.
7. No regression in error rate or user-visible loading failures (parity manual QA).

## 10. Risks & Mitigations
| Risk | Impact | Mitigation |
|------|--------|-----------|
| Underestimated hidden dependencies | Delays factoring | Early grep & static scan to surface globals |
| Increased bundle size from wrapper | Performance | Tree-shake wrapper; reuse in other views |
| Data token drift vs later global store slices | Rework | Choose token names matching planned slice names |
| Telemetry noise | Hard comparison | Single aggregated event with structured subfields |

## 11. Out of Scope / Follow-ups
* Adopting streaming for activity feed (defer to Protocol V2 streaming once available).
* Cross-webview shared slice subscription (future global store introduction).
* Registry-based prefetch/prefault.

---
End of Draft v0.1
