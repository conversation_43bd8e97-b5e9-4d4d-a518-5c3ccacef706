// Simple metrics collection for Home webview refactoring
export function initializeMetricsCollection(): void {
	console.log('📊 Home webview metrics collection initialized');
	
	// Listen for the home-ready event to collect metrics
	window.addEventListener('home-ready', (event: Event) => {
		const customEvent = event as CustomEvent<any>;
		const metrics = customEvent.detail;
		
		console.log('🏠 Home webview ready with metrics:', metrics);
	});
}

// Initialize metrics collection when this module is imported
initializeMetricsCollection();
