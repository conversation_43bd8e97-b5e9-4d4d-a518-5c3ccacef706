import { html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { when } from 'lit/directives/when.js';
import { fromBase64ToString } from '@env/base64';
import type { State } from '../../../home/<USER>';
import { WebviewReadyCommand } from '../../../protocol';
import { scrollableBase } from '../../shared/components/styles/lit/base.css';
import type { LegacyIpc } from '../../shared/ipc';
import { HostIpc } from '../../shared/ipc';
import { homeBaseStyles, homeStyles } from '../home.css';
import type { HomeDataRequester, HomeDataTokens } from '../requestData';
import { createHomeDataRequester, createLegacyIpcWithState } from '../requestData';

// Import all the required components
import '../../plus/shared/components/home-header';
import '../../plus/home/<USER>/active-work';
import '../../plus/home/<USER>/launchpad';
import '../../plus/home/<USER>/overview';
import './feature-nav';
import './ai-all-access-banner';
import './ama-banner';
import './integration-banner';
import './preview-banner';
import '../../shared/components/mcp-banner';
import './promo-banner';
import './repo-alerts';
import '../../shared/components/banner/banner';
import '../../shared/components/skeleton-loader';
import '../homeMetrics'; // Initialize metrics collection

export interface HomeTelemetryData {
	timeToReadyMs: number;
	ipcCallsInitial: number;
	initialHydrationBytes: number;
	failedTokenCount: number;
	lazyTokenDelayMs?: number;
}

@customElement('gitlens-app-home')
export class GitLensAppHome extends LitElement {
	static override styles = [homeBaseStyles, scrollableBase, homeStyles];

	@property({ type: String })
	name!: string;

	@property({ type: String })
	placement!: string;

	@property({ type: String })
	bootstrap!: string;

	@state()
	private _ready = false;

	@state()
	private _loading = true;

	@state()
	private _error: string | undefined;

	@state()
	private _homeData: Partial<HomeDataTokens> = {};

	@state()
	private _lazyData: Partial<HomeDataTokens> = {};

	private _ipc!: HostIpc;
	private _legacyIpc!: LegacyIpc;
	private _dataRequester!: HomeDataRequester;
	private _initialState!: State;
	private _abortController?: AbortController;
	private _telemetryData: HomeTelemetryData = {
		timeToReadyMs: 0,
		ipcCallsInitial: 0,
		initialHydrationBytes: 0,
		failedTokenCount: 0,
	};
	private _constructTime = performance.now();
	private _firstUpdatedTime?: number;
	private _readyTime?: number;

	override connectedCallback(): void {
		super.connectedCallback?.();

		void this.initializeApp();
	}

	override disconnectedCallback(): void {
		super.disconnectedCallback?.();
		this._abortController?.abort();
	}

	override firstUpdated(): void {
		this._firstUpdatedTime = performance.now();
	}

	private async initializeApp(): Promise<void> {
		try {
			// Initialize IPC
			this._ipc = new HostIpc(this.name);

			// Parse initial state
			this._initialState = JSON.parse(fromBase64ToString(this.bootstrap));
			this._legacyIpc = createLegacyIpcWithState(this._ipc, this._initialState);
			this._dataRequester = createHomeDataRequester(this._legacyIpc, this._initialState);

			// Send ready command
			this._ipc.sendCommand(WebviewReadyCommand, undefined);

			// Load blocking tokens
			await this.loadBlockingTokens();

			// Mark as ready
			this._ready = true;
			this._loading = false;
			this._readyTime = performance.now();
			this.calculateTelemetry();

			// Load lazy tokens after ready
			await this.loadLazyTokens();
		} catch (error) {
			this._error = error instanceof Error ? error.message : 'Unknown error';
			this._loading = false;
		}
	}

	private async loadBlockingTokens(): Promise<void> {
		this._abortController = new AbortController();

		try {
			const blockingData = await this._dataRequester.requestBlockingTokens({
				abortSignal: this._abortController.signal,
			});

			this._homeData = { ...this._homeData, ...blockingData };
			this.requestUpdate();
		} catch (error) {
			if (error instanceof Error && error.name !== 'AbortError') {
				console.warn('Failed to load blocking tokens:', error);
				this._telemetryData.failedTokenCount++;
			}
		}
	}

	private async loadLazyTokens(): Promise<void> {
		if (!this._ready) return;

		const lazyStartTime = performance.now();

		try {
			const lazyData = await this._dataRequester.requestLazyTokens({
				abortSignal: this._abortController?.signal,
			});

			this._lazyData = { ...this._lazyData, ...lazyData };
			this._telemetryData.lazyTokenDelayMs = performance.now() - lazyStartTime;
			this.requestUpdate();
		} catch (error) {
			if (error instanceof Error && error.name !== 'AbortError') {
				console.warn('Failed to load lazy tokens:', error);
			}
		}
	}

	private calculateTelemetry(): void {
		if (!this._readyTime || !this._firstUpdatedTime) return;

		this._telemetryData.timeToReadyMs = this._readyTime - this._constructTime;

		const telemetryData = this._dataRequester.getTelemetryData();
		this._telemetryData.ipcCallsInitial = telemetryData.length;
		this._telemetryData.initialHydrationBytes = telemetryData.reduce((sum, data) => sum + data.payloadBytes, 0);

		// Emit telemetry event
		this.emitTelemetryEvent();
	}

	private emitTelemetryEvent(): void {
		// Emit the home.ready telemetry event
		this.dispatchEvent(
			new CustomEvent('home-ready', {
				detail: this._telemetryData,
				bubbles: true,
			}),
		);
	}

	override render(): unknown {
		if (this._error) {
			return this.renderError();
		}

		if (this._loading) {
			return this.renderLoading();
		}

		return this.renderHome();
	}

	private renderError(): unknown {
		return html`
			<div class="home scrollable">
				<div class="error">
					<h2>Error loading Home</h2>
					<p>${this._error}</p>
					<button @click=${this.retry}>Retry</button>
				</div>
			</div>
		`;
	}

	private renderLoading(): unknown {
		return html`
			<div class="home scrollable">
				<div class="loading">
					<skeleton-loader lines="3"></skeleton-loader>
				</div>
			</div>
		`;
	}

	private renderHome(): unknown {
		// Use initial state for now, will be replaced with token data
		const state = this._initialState;
		const badgeSource = { source: 'home', detail: 'badge' };

		return html`
			<div class="home scrollable">
				<gl-home-header class="home__header"></gl-home-header>
				${when(!state?.previewEnabled, () => html`<gl-preview-banner></gl-preview-banner>`)}
				${when(state?.amaBannerCollapsed === false, () => html`<gl-ama-banner></gl-ama-banner>`)}
				<gl-repo-alerts class="home__alerts"></gl-repo-alerts>
				<main class="home__main scrollable" id="main">
					${when(
						state?.previewEnabled === true,
						() => html`
							<gl-preview-banner></gl-preview-banner>
							<gl-ai-all-access-banner></gl-ai-all-access-banner>
							<gl-mcp-banner
								.layout=${'responsive'}
								.source=${'home'}
								.canAutoRegister=${state?.mcpCanAutoRegister ?? false}
								.collapsed=${state?.mcpBannerCollapsed ?? true}
							></gl-mcp-banner>
							<gl-active-work></gl-active-work>
							<gl-launchpad></gl-launchpad>
							<gl-overview></gl-overview>
						`,
						() => html`
							<gl-ai-all-access-banner></gl-ai-all-access-banner>
							<gl-mcp-banner
								.layout=${'responsive'}
								.source=${'home'}
								.collapsed=${state?.mcpBannerCollapsed ?? true}
							></gl-mcp-banner>
							<gl-feature-nav .badgeSource=${badgeSource}></gl-feature-nav>
						`,
					)}
				</main>
			</div>
		`;
	}

	private retry(): void {
		this._error = undefined;
		this._loading = true;
		this._ready = false;
		this._homeData = {};
		this._lazyData = {};
		void this.initializeApp();
	}
}
