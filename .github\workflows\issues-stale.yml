name: 'Close stale issues'

on:
    schedule:
        - cron: '00 5 * * *'
    workflow_dispatch:

jobs:
    stale:
        runs-on: ubuntu-latest
        permissions:
            issues: write
            pull-requests: write
        steps:
            - uses: actions/stale@v9
              with:
                  repo-token: ${{ secrets.GITHUB_TOKEN }}
                  days-before-close: 7
                  days-before-stale: 7
                  only-labels: 'needs-more-info'
                  exempt-issue-labels: 'triage'
                  close-issue-message: 'Closing this issue because it needs more information and has not had recent activity. Please re-open this issue if more details can be provided. Thanks!'
                  stale-issue-label: 'inactive'
                  stale-issue-message: 'This issue needs more information and has not had recent activity. Please provide the missing information or it will be closed in 7 days. Thanks!'
                  labels-to-add-when-unstale: 'triage'
                  labels-to-remove-when-unstale: 'needs-more-info'
