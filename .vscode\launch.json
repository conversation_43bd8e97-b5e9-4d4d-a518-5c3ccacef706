// A launch configuration that compiles the extension and then opens it inside a new window
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": ["--profile=Debugging (GitLens)", "--extensionDevelopmentPath=${workspaceFolder}"],
			"debugWebviews": true,
			"rendererDebugOptions": {
				"outFiles": ["${workspaceFolder}/dist/webviews/**/*.js"],
				"pauseForSourceMap": true,
				"showAsyncStacks": true,
				"smartStep": true,
				"sourceMapRenames": true,
				"sourceMaps": true,
				"trace": true,
				// "urlFilter": "*eamodio.gitlens*",
				"webRoot": "${workspaceFolder}/src/webviews/apps"
			},
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"pauseForSourceMap": true,
			"runtimeSourcemapPausePatterns": ["${workspaceFolder}/dist/**/*.js"],
			"presentation": {
				"group": "2_run",
				"order": 1
			},
			"showAsyncStacks": true,
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true,
			"trace": true
		},
		{
			"name": "Run (web)",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": [
				// "--folder-uri=vscode-vfs://github/gitkraken/vscode-gitlens",
				"--profile=Debugging (GitLens)",
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionDevelopmentKind=web"
			],
			"debugWebviews": true,
			"debugWebWorkerHost": true,
			"rendererDebugOptions": {
				"outFiles": ["${workspaceFolder}/dist/browser/**/*.js", "${workspaceFolder}/dist/webviews/**/*.js"],
				"pauseForSourceMap": true,
				"showAsyncStacks": true,
				"smartStep": true,
				"sourceMapRenames": true,
				"sourceMaps": true,
				"trace": true,
				// "urlFilter": "*eamodio.gitlens*",
				"webRoot": "${workspaceFolder}"
			},
			"outFiles": ["${workspaceFolder}/dist/browser/**/*.js"],
			"pauseForSourceMap": true,
			"runtimeSourcemapPausePatterns": ["${workspaceFolder}/dist/**/*.js"],
			"presentation": {
				"group": "2_run",
				"order": 2
			},
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"showAsyncStacks": true,
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true,
			"trace": true
		},
		{
			"name": "Run (sandboxed)",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": ["--profile-temp", "--extensionDevelopmentPath=${workspaceFolder}"],
			"debugWebviews": true,
			"rendererDebugOptions": {
				"outFiles": ["${workspaceFolder}/dist/webviews/**/*.js"],
				"pauseForSourceMap": true,
				"showAsyncStacks": true,
				"smartStep": true,
				"sourceMapRenames": true,
				"sourceMaps": true,
				"trace": true,
				// "urlFilter": "*eamodio.gitlens*",
				"webRoot": "${workspaceFolder}/src/webviews/apps"
			},
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"pauseForSourceMap": true,
			"runtimeSourcemapPausePatterns": ["${workspaceFolder}/dist/**/*.js"],
			"presentation": {
				"group": "2_run",
				"order": 3
			},
			"showAsyncStacks": true,
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true,
			"trace": true
		},
		{
			"name": "Run Tests",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": [
				"--profile-temp",
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionTestsPath=${workspaceFolder}/out/tests"
			],
			"outFiles": ["${workspaceFolder}/out/tests/*.test.js"],
			"preLaunchTask": "npm: build:tests",
			"presentation": {
				"group": "2_run_tests",
				"order": 2
			},
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true
		},
		{
			"name": "Watch & Run",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": ["--profile=Debugging (GitLens)", "--extensionDevelopmentPath=${workspaceFolder}"],
			"debugWebviews": true,
			"rendererDebugOptions": {
				"outFiles": ["${workspaceFolder}/dist/webviews/**/*.js"],
				"pauseForSourceMap": true,
				"showAsyncStacks": true,
				"smartStep": true,
				"sourceMapRenames": true,
				"sourceMaps": true,
				"trace": true,
				// "urlFilter": "*eamodio.gitlens*",
				"webRoot": "${workspaceFolder}/src/webviews/apps"
			},
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"pauseForSourceMap": true,
			"runtimeSourcemapPausePatterns": ["${workspaceFolder}/dist/**/*.js"],
			"preLaunchTask": "${defaultBuildTask}",
			"presentation": {
				"group": "1_watch",
				"order": 1
			},
			"showAsyncStacks": true,
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true,
			"trace": true
		},
		{
			"name": "Watch & Run (web)",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": [
				// "--folder-uri=vscode-vfs://github/gitkraken/vscode-gitlens",
				"--profile=Debugging (GitLens)",
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionDevelopmentKind=web"
			],
			"debugWebviews": true,
			"debugWebWorkerHost": true,
			"rendererDebugOptions": {
				"outFiles": ["${workspaceFolder}/dist/browser/**/*.js", "${workspaceFolder}/dist/webviews/**/*.js"],
				"pauseForSourceMap": true,
				"showAsyncStacks": true,
				"smartStep": true,
				"sourceMapRenames": true,
				"sourceMaps": true,
				"trace": true,
				// "urlFilter": "*eamodio.gitlens*",
				"webRoot": "${workspaceFolder}"
			},
			"outFiles": ["${workspaceFolder}/dist/browser/**/*.js"],
			"pauseForSourceMap": true,
			"runtimeSourcemapPausePatterns": ["${workspaceFolder}/dist/**/*.js"],
			"preLaunchTask": "${defaultBuildTask}",
			"presentation": {
				"group": "1_watch",
				"order": 1
			},
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"showAsyncStacks": true,
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true,
			"trace": true
		},
		{
			"name": "Watch & Run (sandboxed)",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": ["--profile-temp", "--extensionDevelopmentPath=${workspaceFolder}"],
			"debugWebviews": true,
			"rendererDebugOptions": {
				"pauseForSourceMap": true,
				"showAsyncStacks": true,
				"smartStep": true,
				"sourceMapRenames": true,
				"sourceMaps": true,
				"trace": true,
				// "urlFilter": "*eamodio.gitlens*",
				"webRoot": "${workspaceFolder}/src/webviews/apps"
			},
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"pauseForSourceMap": true,
			"runtimeSourcemapPausePatterns": ["${workspaceFolder}/dist/**/*.js"],
			"preLaunchTask": "${defaultBuildTask}",
			"presentation": {
				"group": "1_watch",
				"order": 2
			},
			"showAsyncStacks": true,
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true,
			"trace": true
		},
		{
			"name": "Watch & Run Tests",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--profile-temp",
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionTestsPath=${workspaceFolder}/out/test/suite/index"
			],
			"outFiles": ["${workspaceFolder}/out/test/**/*.js", "${workspaceFolder}/dist/**/*.js"],
			"preLaunchTask": "npm: watch:tests",
			"presentation": {
				"group": "1_watch_tests",
				"order": 2
			},
			"skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
			"smartStep": true,
			"sourceMapRenames": true,
			"sourceMaps": true
		}
	]
}
