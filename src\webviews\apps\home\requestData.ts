import type { DataToken, LegacyIpc, BatchTokenResponse } from '../shared/ipc';
import { LegacyIpc as LegacyIpcImpl } from '../shared/ipc';
import type { State } from '../../home/<USER>';

export interface RequestDataOptions {
	timeout?: number;
	abortSignal?: AbortSignal;
}

export interface HomeDataTokens {
	user: {
		avatar?: string;
		organizationsCount?: number;
	};
	subscription: State['subscription'];
	repos: State['repositories'];
	activeRepo: any; // GetActiveOverviewResponse
	recentActivity: any; // GetInactiveOverviewResponse
	promotions: any; // Future promotions data
}

export type BlockingTokens = 'user' | 'subscription' | 'repos' | 'activeRepo';
export type LazyTokens = 'recentActivity' | 'promotions';

export class HomeDataRequester {
	private readonly _legacyIpc: LegacyIpc;
	private readonly _initialState: State;

	constructor(legacyIpc: LegacyIpc, initialState: State) {
		this._legacyIpc = legacyIpc;
		this._initialState = initialState;
	}

	async requestBlockingTokens(options?: RequestDataOptions): Promise<Partial<HomeDataTokens>> {
		const blockingTokens: BlockingTokens[] = ['user', 'subscription', 'repos', 'activeRepo'];

		try {
			const batchResponse = await this._legacyIpc.requestTokensBatch(blockingTokens, options);
			return this.processTokenResponses(batchResponse);
		} catch (error) {
			// Fallback to individual requests
			const results: Partial<HomeDataTokens> = {};

			for (const token of blockingTokens) {
				try {
					const response = await this._legacyIpc.requestToken(token, options);
					if (!response.error) {
						(results as any)[token] = response.data;
					}
				} catch {
					// Use fallback data from initial state
					(results as any)[token] = this.getFallbackData(token);
				}
			}

			return results;
		}
	}

	async requestLazyTokens(options?: RequestDataOptions): Promise<Partial<HomeDataTokens>> {
		const lazyTokens: LazyTokens[] = ['recentActivity', 'promotions'];

		try {
			const batchResponse = await this._legacyIpc.requestTokensBatch(lazyTokens, options);
			return this.processTokenResponses(batchResponse);
		} catch (error) {
			// Fallback to individual requests
			const results: Partial<HomeDataTokens> = {};

			for (const token of lazyTokens) {
				try {
					const response = await this._legacyIpc.requestToken(token, options);
					if (!response.error) {
						(results as any)[token] = response.data;
					}
				} catch {
					// Lazy tokens can fail without blocking
					console.warn(`Failed to load lazy token: ${token}`);
				}
			}

			return results;
		}
	}

	async requestToken<K extends DataToken>(
		token: K,
		options?: RequestDataOptions,
	): Promise<HomeDataTokens[K] | undefined> {
		try {
			const response = await this._legacyIpc.requestToken<HomeDataTokens[K]>(token, options);
			if (response.error) {
				console.warn(`Token request failed: ${token}`, response.error);
				return this.getFallbackData(token);
			}
			return response.data;
		} catch (error) {
			console.warn(`Token request error: ${token}`, error);
			return this.getFallbackData(token);
		}
	}

	private processTokenResponses(batchResponse: BatchTokenResponse): Partial<HomeDataTokens> {
		const results: Partial<HomeDataTokens> = {};

		for (const response of batchResponse.responses) {
			if (response.error) {
				console.warn(`Token response error: ${response.token}`, response.error);
				(results as any)[response.token] = this.getFallbackData(response.token);
			} else {
				(results as any)[response.token] = response.data;
			}
		}

		return results;
	}

	private getFallbackData(token: DataToken): any {
		switch (token) {
			case 'user':
				return {
					avatar: this._initialState.avatar,
					organizationsCount: this._initialState.organizationsCount,
				};
			case 'subscription':
				return this._initialState.subscription;
			case 'repos':
				return this._initialState.repositories;
			case 'activeRepo':
			case 'recentActivity':
			case 'promotions':
				return undefined; // These don't have fallback data
			default:
				return undefined;
		}
	}

	getTelemetryData() {
		return this._legacyIpc.getTelemetryData();
	}

	clearTelemetryData() {
		this._legacyIpc.clearTelemetryData();
	}
}

// Helper function to create a data requester
export function createHomeDataRequester(legacyIpc: LegacyIpc, initialState: State): HomeDataRequester {
	return new HomeDataRequester(legacyIpc, initialState);
}

// Helper function to create a legacy IPC with initial state
export function createLegacyIpcWithState(ipc: any, initialState: State): LegacyIpc {
	return new LegacyIpcImpl(ipc, initialState);
}

// Helper function to determine if a token is blocking
export function isBlockingToken(token: DataToken): token is BlockingTokens {
	return ['user', 'subscription', 'repos', 'activeRepo'].includes(token);
}

// Helper function to determine if a token is lazy
export function isLazyToken(token: DataToken): token is LazyTokens {
	return ['recentActivity', 'promotions'].includes(token);
}
