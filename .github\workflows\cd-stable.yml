name: Publish Stable

on:
    push:
        tags:
            - 'v*.*.*' # Push events to matching v*, i.e. v1.0, v20.15.10

jobs:
    build:
        name: Publish Stable
        runs-on: ubuntu-latest
        permissions:
            contents: write
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup node
              uses: actions/setup-node@v4
              with:
                  node-version: '22'
            - name: Setup pnpm
              uses: pnpm/action-setup@v3
              with:
                  version: 10
            - name: Setup Environment
              run: node -e "console.log('PACKAGE_VERSION=' + require('./package.json').version + '\nPACKAGE_NAME=' + require('./package.json').name + '-' + require('./package.json').version)" >> $GITHUB_ENV
            - name: Verify versions
              run: node -e "if ('refs/tags/v' + '${{ env.PACKAGE_VERSION }}' !== '${{ github.ref }}') { console.log('::error' + 'Version Mismatch. refs/tags/v' + '${{ env.PACKAGE_VERSION }}', '${{ github.ref }}'); throw Error('Version Mismatch')} "
            - name: Install
              run: pnpm install
            - name: Package extension
              run: pnpm run package
            - name: Publish Extension to VS Code Marketplace
              run: pnpm vsce publish --no-dependencies --packagePath ./${{ env.PACKAGE_NAME }}.vsix -p ${{ secrets.GITLENS_VSCODE_MARKETPLACE_PAT }}
            - name: Publish extension to OpenVSIX
              run: pnpm ovsx publish ./${{ env.PACKAGE_NAME }}.vsix -p ${{ secrets.GITLENS_OPENVSIX_PAT }}
            - name: Generate Changelog
              id: changelog
              uses: mindsers/changelog-reader-action@v2
              with:
                  version: ${{ env.PACKAGE_VERSION }}
                  path: ./CHANGELOG.md
            - name: Create GitHub release
              id: create_release
              uses: softprops/action-gh-release@v2
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              with:
                  tag_name: ${{ github.ref }}
                  name: v${{ env.PACKAGE_VERSION }}
                  body: ${{ steps.changelog.outputs.changes }}
                  draft: false
                  prerelease: false
                  files: ./${{ env.PACKAGE_NAME }}.vsix
